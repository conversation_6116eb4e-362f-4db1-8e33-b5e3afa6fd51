#!/usr/bin/env python3
"""
批量导入文档到RAG系统
用于将data目录中的文档导入到ChromaDB向量数据库
"""

import sys
import os
import logging
from pathlib import Path
import asyncio
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def import_documents():
    """批量导入文档"""
    try:
        # 导入RAG服务
        from backend.app.rag_service import RAGService
        from backend.config import settings
        
        print("🚀 开始批量导入文档到RAG系统")
        print("=" * 60)
        
        # 初始化RAG服务
        print("📋 初始化RAG服务...")
        rag_service = RAGService()
        print("✅ RAG服务初始化成功")
        
        # 获取data目录中的所有txt文件
        data_dir = Path(settings.data_dir)
        if not data_dir.exists():
            print(f"❌ 数据目录不存在: {data_dir}")
            return False
        
        # 查找所有txt文件
        txt_files = list(data_dir.glob("*.txt"))
        if not txt_files:
            print(f"❌ 在 {data_dir} 中没有找到txt文件")
            return False
        
        print(f"📁 找到 {len(txt_files)} 个txt文件")
        
        # 检查当前文档列表
        current_docs = rag_service.get_documents_list()
        print(f"📊 当前RAG系统中有 {len(current_docs)} 个文档")
        
        # 批量处理文档
        success_count = 0
        error_count = 0
        
        for i, file_path in enumerate(txt_files, 1):
            try:
                print(f"\n📄 处理文档 {i}/{len(txt_files)}: {file_path.name}")
                
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if not content.strip():
                    print(f"⚠️  文件为空，跳过: {file_path.name}")
                    continue
                
                print(f"   文件大小: {len(content)} 字符")
                
                # 处理文档
                result = await rag_service.process_document(
                    content=content,
                    filename=file_path.name,
                    file_type="text/plain"
                )
                
                if result.get("success"):
                    success_count += 1
                    print(f"✅ 成功导入: {file_path.name}")
                    if "chunks_count" in result:
                        print(f"   分块数量: {result['chunks_count']}")
                else:
                    error_count += 1
                    error_msg = result.get("error", "未知错误")
                    print(f"❌ 导入失败: {file_path.name} - {error_msg}")
                
            except Exception as e:
                error_count += 1
                print(f"❌ 处理文件时出错: {file_path.name} - {e}")
                logger.error(f"处理文件 {file_path.name} 时出错: {e}", exc_info=True)
        
        print("\n" + "=" * 60)
        print("📊 导入结果总结:")
        print(f"   总文件数: {len(txt_files)}")
        print(f"   成功导入: {success_count}")
        print(f"   导入失败: {error_count}")
        
        # 检查导入后的文档列表
        final_docs = rag_service.get_documents_list()
        print(f"   最终文档数: {len(final_docs)}")
        
        if success_count > 0:
            print("\n🎉 批量导入完成！")
            print("💡 提示：现在可以在前端页面查看导入的文档列表")
            return True
        else:
            print("\n⚠️  没有成功导入任何文档")
            return False
            
    except Exception as e:
        print(f"❌ 批量导入过程中出错: {e}")
        logger.error(f"批量导入出错: {e}", exc_info=True)
        return False

async def check_rag_status():
    """检查RAG系统状态"""
    try:
        from backend.app.rag_service import RAGService
        from backend.config import settings
        
        print("🔍 检查RAG系统状态...")
        
        # 检查配置
        print(f"✅ 数据目录: {settings.data_dir}")
        print(f"✅ 存储目录: {settings.storage_dir}")
        print(f"✅ ChromaDB目录: {settings.chroma_persist_directory}")
        print(f"✅ 集合名称: {settings.collection_name}")
        
        # 检查目录是否存在
        data_dir = Path(settings.data_dir)
        storage_dir = Path(settings.storage_dir)
        chroma_dir = Path(settings.chroma_persist_directory)
        
        print(f"📁 数据目录存在: {data_dir.exists()}")
        print(f"📁 存储目录存在: {storage_dir.exists()}")
        print(f"📁 ChromaDB目录存在: {chroma_dir.exists()}")
        
        # 检查ChromaDB文件
        chroma_db_file = chroma_dir / "chroma.sqlite3"
        print(f"🗄️  ChromaDB文件存在: {chroma_db_file.exists()}")
        if chroma_db_file.exists():
            file_size = chroma_db_file.stat().st_size
            print(f"   文件大小: {file_size} 字节")
        
        # 初始化RAG服务并检查
        rag_service = RAGService()
        docs = rag_service.get_documents_list()
        print(f"📊 当前文档数量: {len(docs)}")
        
        if docs:
            print("📋 现有文档列表:")
            for doc in docs[:5]:  # 只显示前5个
                print(f"   - {doc['filename']} ({doc['chunks_count']} 块)")
            if len(docs) > 5:
                print(f"   ... 还有 {len(docs) - 5} 个文档")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查RAG状态时出错: {e}")
        logger.error(f"检查RAG状态出错: {e}", exc_info=True)
        return False

async def main():
    """主函数"""
    print("🚀 RAG系统文档批量导入工具")
    print("=" * 60)
    
    # 检查RAG系统状态
    status_ok = await check_rag_status()
    if not status_ok:
        print("❌ RAG系统状态检查失败")
        return False
    
    print("\n" + "=" * 60)
    
    # 询问是否继续导入
    response = input("是否继续批量导入文档？(y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("取消导入操作")
        return True
    
    # 执行批量导入
    success = await import_documents()
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)
