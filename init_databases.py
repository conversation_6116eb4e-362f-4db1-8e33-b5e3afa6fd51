#!/usr/bin/env python3
"""
数据库初始化脚本
用于初始化SQLite和ChromaDB数据库
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_chroma_db():
    """初始化ChromaDB数据库"""
    try:
        import chromadb
        from backend.config import settings
        
        logger.info("开始初始化ChromaDB...")
        logger.info(f"ChromaDB版本: {chromadb.__version__}")
        logger.info(f"存储目录: {settings.chroma_persist_directory}")
        logger.info(f"集合名称: {settings.collection_name}")
        
        # 确保存储目录存在
        storage_path = Path(settings.chroma_persist_directory)
        storage_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"存储目录已创建: {storage_path.absolute()}")
        
        # 创建ChromaDB客户端
        client = chromadb.PersistentClient(
            path=settings.chroma_persist_directory
        )
        logger.info("ChromaDB客户端创建成功")
        
        # 创建或获取集合
        try:
            collection = client.get_collection(name=settings.collection_name)
            logger.info(f"找到现有集合: {settings.collection_name}")
        except Exception:
            collection = client.create_collection(name=settings.collection_name)
            logger.info(f"创建新集合: {settings.collection_name}")
        
        # 检查集合信息
        count = collection.count()
        logger.info(f"集合中的文档数量: {count}")
        
        return True
        
    except Exception as e:
        logger.error(f"ChromaDB初始化失败: {e}")
        return False

def check_sqlite_files():
    """检查SQLite数据库文件"""
    try:
        from backend.config import settings
        
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        logger.info("检查SQLite数据库文件:")
        if sqlite_files:
            for file in sqlite_files:
                size = file.stat().st_size
                logger.info(f"  - {file.name}: {size} bytes")
        else:
            logger.warning("  未找到SQLite数据库文件")
            
        return len(sqlite_files) > 0
        
    except Exception as e:
        logger.error(f"检查SQLite文件失败: {e}")
        return False

def verify_database_structure():
    """验证数据库结构"""
    try:
        import sqlite3
        from backend.config import settings
        
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        if not sqlite_files:
            logger.warning("没有SQLite文件可供验证")
            return False
            
        db_file = sqlite_files[0]
        logger.info(f"验证数据库结构: {db_file}")
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            logger.info("数据库表结构:")
            for table in tables:
                table_name = table[0]
                logger.info(f"  - {table_name}")
                
                # 获取表的列信息
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                for col in columns:
                    logger.info(f"    {col[1]} {col[2]}")
                    
        return True
        
    except Exception as e:
        logger.error(f"验证数据库结构失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("开始数据库初始化")
    logger.info("=" * 50)
    
    # 步骤1: 初始化ChromaDB
    logger.info("\n步骤1: 初始化ChromaDB")
    chroma_success = init_chroma_db()
    
    # 步骤2: 检查SQLite文件
    logger.info("\n步骤2: 检查SQLite文件")
    sqlite_success = check_sqlite_files()
    
    # 步骤3: 验证数据库结构
    logger.info("\n步骤3: 验证数据库结构")
    structure_success = verify_database_structure()
    
    # 总结
    logger.info("\n" + "=" * 50)
    logger.info("初始化结果总结:")
    logger.info(f"  ChromaDB初始化: {'✅ 成功' if chroma_success else '❌ 失败'}")
    logger.info(f"  SQLite文件检查: {'✅ 成功' if sqlite_success else '❌ 失败'}")
    logger.info(f"  数据库结构验证: {'✅ 成功' if structure_success else '❌ 失败'}")
    
    if chroma_success and sqlite_success:
        logger.info("🎉 数据库初始化完成!")
        return True
    else:
        logger.error("❌ 数据库初始化失败!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
